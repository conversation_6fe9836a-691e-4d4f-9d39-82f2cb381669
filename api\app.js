const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const { status: httpStatus } = require("http-status");
const morgan = require("./config/morgan");
const config = require('./config/config');
const passport = require('./config/passport');
const apiLimiter = require("./middlewares/rateLimiter");
const routes = require("./routes");
const { errorConverter, errorHandler } = require("./middlewares/error");
const { ApiError } = require("./helpers/api.helper");

const app = express();

// trust proxy for rate limiting when behind gateway (specific to localhost and docker)
if (config.auth.mode === 'tyk')
  app.set('trust proxy', ['loopback', 'linklocal', 'uniquelocal']);

// request logging
app.use(morgan.successHandler);
app.use(morgan.errorHandler);

// set security HTTP headers
// Configure CSP to allow Swagger UI to connect to Tyk gateway when in tyk mode
const helmetConfig = config.auth.mode === 'tyk' ? {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      connectSrc: ["'self'", "http://localhost:8181", "https://localhost:8181"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "https:", "data:"]
    }
  }
} : {};

app.use(helmet(helmetConfig));
// parse json request body
app.use(express.json());

// parse urlencoded request body
app.use(express.urlencoded({ extended: true }));

// enable cors
app.use(cors());
app.options("*", cors());

// passport authentication
app.use(passport.initialize());

// limit repeated failed requests
app.use("/", apiLimiter);

// api routes
app.use("/", routes);

// Handle WebSocket endpoint requests (to prevent error logs)
app.all("/ws", (req, res) => {
  res.status(404).json({
    status: false,
    message: "WebSocket endpoint not available",
    note: "This application does not support WebSocket connections"
  });
});

// send back a 404 error for any unknown api request
app.use((req, res, next) => {
  next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
});

// convert error to ApiError, if needed
app.use(errorConverter);

// handle error
app.use(errorHandler);

module.exports = app;
