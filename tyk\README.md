# CareMate Tyk Gateway

## About
This repository contains the Tyk Gateway configuration for the CareMate API project. The Tyk Gateway acts as an API gateway providing authentication, authorization, analytics, and rate-limiting capabilities for the CareMate API.

## Architecture Overview
The CareMate project consists of two main components:
- **API Project** (`../api/`) - The main API server that handles business logic
- **Tyk Gateway** (`./`) - API gateway that provides authentication, authorization, and routing

### Authentication Modes
The API project can be configured to work in two authentication modes via the `AUTH_MODE` environment variable:

1. **Custom Mode** (`AUTH_MODE=custom`) - API handles authentication directly using JWT tokens
2. **Tyk Mode** (`AUTH_MODE=tyk`) - Tyk Gateway handles authentication and passes validated user information to the API

When `AUTH_MODE=tyk`, the API project uses different server URLs:
- `PUBLIC_SERVER_URL` - For public endpoints routed through Tyk (e.g., `http://localhost:8181/caremate/api`)
- `PROTECTED_SERVER_URL` - For protected endpoints routed through Tyk (e.g., `http://localhost:8181/caremate/protected`)

## Project Structure

### API Definitions (`./apps/`)
- `caremate-public-api.json` - Public endpoints (authentication, health checks, master data)
- `caremate-jwt-api.json` - JWT-protected endpoints with permission-based authorization

### Middleware (`./middleware/`)
- `caremate-public.js` - Middleware for public endpoints that sets dummy headers for API compatibility
- `caremate-auth.js` - Permission-based authorization middleware for protected endpoints

### Configuration
- `tyk.standalone.conf` - Tyk Gateway configuration
- `docker-compose.yml` - Docker setup for Tyk Gateway and Redis
- `test-caremate-auth.js` - Comprehensive test script for authentication and authorization

## Requirements
1. **Redis** - Tyk gateway requires a running Redis instance for session storage and analytics
2. **Docker & Docker Compose** - For running the gateway and Redis containers
3. **CareMate API** - The main API server running on port 3001

## Gateway Configuration

### Dual API Setup
The CareMate Tyk Gateway is configured with two separate API definitions:

1. **Public API** (`/caremate/api/`) - Keyless endpoints for:
   - Authentication (`/auth/login`, `/auth/register`)
   - Health checks (`/health`)
   - Master data (countries, states, timezones)
   - Site settings

2. **Protected API** (`/caremate/protected/`) - JWT-authenticated endpoints for:
   - All business logic endpoints requiring authentication
   - Permission-based authorization via middleware
   - User management, facilities, appointments, etc.

### Authentication Flow
1. Client authenticates via `/caremate/api/auth/login` (public endpoint)
2. API returns JWT token with embedded user permissions
3. Client includes JWT token in Authorization header for protected endpoints
4. Tyk Gateway validates JWT signature and passes request to middleware
5. CareMate middleware validates permissions and sets headers for API
6. API receives validated user information via headers

### Environment Configuration
The API project's authentication mode is controlled by the `AUTH_MODE` environment variable:

```bash
# In .env.local or .env.dev
AUTH_MODE=tyk  # Enable Tyk Gateway mode
# or
AUTH_MODE=custom  # Direct API authentication (bypass gateway)
```

When `AUTH_MODE=tyk`, the API uses gateway URLs:
- `PUBLIC_SERVER_URL=http://localhost:8181/caremate/api`
- `PROTECTED_SERVER_URL=http://localhost:8181/caremate/protected`

### Testing Setup
```bash
# Run the CareMate authentication test suite
node test-caremate-auth.js

# Test credentials available:
# Admin: <EMAIL> / Pa$w0rd!
# Kiosk: <EMAIL> / Pa$w0rd!
```

### Gateway Endpoints
- **Public API**: `http://localhost:8181/caremate/api/` (no authentication required)
- **Protected API**: `http://localhost:8181/caremate/protected/` (JWT token required)
- **Gateway Admin**: `http://localhost:8181/tyk/apis` (requires X-Tyk-Authorization header)

## Getting Started

### Prerequisites
Before starting, ensure you have the following installed:
- **Docker & Docker Compose** - For running Tyk Gateway and Redis
- **HTTP Client** - For testing API endpoints:
  - Command line: [curl](https://everything.curl.dev/get)
  - GUI: [Postman](https://www.postman.com/downloads/)
  - VS Code: [REST Client extension](https://marketplace.visualstudio.com/items?itemName=humao.rest-client)
- **jq** (optional) - For JSON formatting with curl commands

### Starting the Gateway

1. **Start Tyk Gateway and Redis**:
   ```bash
   docker-compose up -d
   ```

2. **Verify the deployment**:
   ```bash
   curl http://localhost:8181/hello -i
   ```

   Expected response:
   ```json
   {
     "status": "pass",
     "version": "v5.5.0",
     "description": "Tyk GW",
     "details": {
       "redis": {
         "status": "pass",
         "componentType": "datastore",
         "time": "2024-01-01T12:00:00Z"
       }
     }
   }
   ```

3. **Check loaded API definitions**:
   ```bash
   curl http://localhost:8181/tyk/apis -H "X-Tyk-Authorization: foo" | jq .
   ```

   This should return the CareMate API definitions (public and protected APIs).

### Starting the CareMate API

1. **Navigate to the API directory**:
   ```bash
   cd ../api
   ```

2. **Configure environment**:
   ```bash
   # Copy and configure environment file
   cp .env.local.example .env.local

   # Set AUTH_MODE to enable Tyk Gateway
   AUTH_MODE=tyk
   ```

3. **Start the API server**:
   ```bash
   npm install
   npm start
   ```

The API will be available at `http://localhost:3001` but should be accessed through the gateway URLs when `AUTH_MODE=tyk`.

## Testing the Setup

### Basic Gateway Tests

1. **Test public endpoints** (no authentication required):
   ```bash
   # Health check
   curl http://localhost:8181/caremate/api/health

   # Get countries (master data)
   curl http://localhost:8181/caremate/api/countries
   ```

2. **Test authentication**:
   ```bash
   # Login to get JWT token
   curl -X POST http://localhost:8181/caremate/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "Pa$w0rd!"
     }'
   ```

3. **Test protected endpoints** (requires JWT token):
   ```bash
   # Replace YOUR_JWT_TOKEN with the token from login response
   curl http://localhost:8181/caremate/protected/facilities \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

### Automated Testing

Run the comprehensive test suite:
```bash
node test-caremate-auth.js
```

This script tests:
- Public endpoint accessibility
- Authentication flow
- JWT token validation
- Permission-based authorization
- Header injection by middleware

## Configuration Details

### Environment Variables
The gateway uses environment variables from `docker-compose.yml`:
- `JWT_SECRET` - Shared secret for JWT validation (must match API project)

### API Definitions
- **Public API** (`caremate-public-api.json`):
  - Listen path: `/caremate/api/`
  - Target: `http://************:3001/api/`
  - Keyless (no authentication)
  - Uses `caremate-public.js` middleware

- **Protected API** (`caremate-jwt-api.json`):
  - Listen path: `/caremate/protected/`
  - Target: `http://************:3001/api/`
  - JWT authentication enabled
  - Uses `caremate-auth.js` middleware for permissions

### Middleware Functions
- **caremate-public.js**: Sets dummy headers for API compatibility in Tyk mode
- **caremate-auth.js**: Validates JWT tokens and checks user permissions

## Troubleshooting

### Common Issues

1. **Gateway not starting**:
   - Check if Redis is running: `docker-compose logs redis`
   - Verify port 8181 is not in use: `netstat -an | grep 8181`

2. **API definitions not loading**:
   - Check gateway logs: `docker-compose logs gateway`
   - Verify JSON syntax in `./apps/*.json` files

3. **JWT validation failing**:
   - Ensure `JWT_SECRET` matches between gateway and API project
   - Check token format: should be `Bearer <token>`

4. **Permission errors**:
   - Verify user permissions in JWT token payload
   - Check middleware logs for permission validation

5. **Target URL connection issues**:
   - Update target URLs in API definitions to match your API server
   - Ensure API server is accessible from Docker container

### Logs and Debugging

View gateway logs:
```bash
docker-compose logs -f gateway
```

View Redis logs:
```bash
docker-compose logs -f redis
```

Enable debug logging by modifying `tyk.standalone.conf`:
```json
{
  "log_level": "debug"
}
```

## Production Considerations

### Security
- Change default JWT secret in production
- Use proper SSL/TLS certificates
- Configure proper CORS policies
- Set up rate limiting policies

### Performance
- Configure Redis persistence for production
- Set up Redis clustering for high availability
- Monitor gateway performance and analytics
- Configure proper connection pooling

### Deployment
- Use environment-specific API definitions
- Set up proper health checks
- Configure log aggregation
- Set up monitoring and alerting