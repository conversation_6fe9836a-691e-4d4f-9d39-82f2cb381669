{"name": "CareMate Public API", "api_id": "caremate-public", "org_id": "default", "definition": {"location": "header", "key": "version"}, "use_keyless": true, "custom_middleware": {"driver": "otto", "post": [{"name": "CareMatePublic", "path": "./middleware/caremate-public.js", "require_session": false, "raw_body_only": false}]}, "version_data": {"not_versioned": true, "versions": {"Default": {"name": "<PERSON><PERSON><PERSON>"}}}, "proxy": {"listen_path": "/caremate/api/", "target_url": "http://host.docker.internal:3001/api/", "strip_listen_path": true}, "CORS": {"enable": true, "allowed_origins": ["http://localhost:3001", "https://localhost:3001"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"], "allowed_headers": ["Origin", "Accept", "Content-Type", "X-Requested-With", "Authorization"], "exposed_headers": [], "allow_credentials": true, "max_age": 24, "options_passthrough": false, "debug": false}}